export default function PressPage() {
  const pressItems = [
    {
      title: "Danske Kommuner Magazine",
      description: "KLs magasin Danske Kommuner har været på besøg hos Actura Element på Islands Brygge, i forbindelse med den kommende udruldning af Juniormesterlæren i den danske folkeskole."
    },
    {
      title: "Impact Insider",
      description: "Morten vil bygge en drømmefabrik på Amager - den skal lette vejen mod arbejdslivet for elever med autisme"
    },
    {
      title: "TV2 Østjylland",
      description: "Elever med autisme bliver væk fra skole: Virksomhed giver dem gejsten tilbage"
    },
    {
      title: "DR1",
      description: "Robotten 'Lille Troels' gør Lars med autisme bedre i skolen"
    },
    {
      title: "Finans",
      description: "Hvem siger, at autister ikke kan skabe fremtidens teknologi?"
    }
  ]

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-8">Presse</h1>
        
        <div className="mb-12">
          <div className="bg-blue-50 p-8 rounded-lg">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">Velkommen til min drømmefabrik for børn og unge med autisme og ADHD!</h2>
            <p className="text-gray-600 mb-4">
              Jeg er glad og stolt over, at kunne byde jer indenfor i vores tech- og udviklingsvirksomhed på Islands Brygge.
            </p>
            <p className="text-gray-600">
              Med Actura Element, ønsker jeg sammen med gode venner og kollegaer, at skabe et innovativt og trygt univers, 
              hvor vi rækker ud til neurodivergente børn/unge og hjælper dem godt og trygt ind i ungdoms-, voksen- og arbejdslivet.
            </p>
            <p className="text-sm text-gray-500 mt-4">— Morten Olesen, opfinder og direktør</p>
          </div>
        </div>
        
        <div>
          <h2 className="text-2xl font-semibold text-gray-900 mb-8">Presseomtale</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {pressItems.map((item, index) => (
              <div key={index} className="bg-gray-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">{item.title}</h3>
                <p className="text-gray-600">{item.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}