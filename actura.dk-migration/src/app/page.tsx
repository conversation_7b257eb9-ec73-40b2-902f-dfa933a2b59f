'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';

export default function Home() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const slides = [
    {
      quote: "Med Actura Element, ønsker vi, at skabe en innovativ udviklingsvirksomhed og der ved siden af vores business fokus ønsker at skabe et trygt univers der rækker ud til neurodivergente børn og unge. Vi hjælper dem godt og trygt ind i ungdoms-, voksen- og arbejdslivet.",
      author: "Morten Olesen / opfinder og direktør\nAlvin <PERSON> / partner",
      title: "actura element"
    },
    {
      title: "Vactura Visuals",
      subtitle: "actura visuals",
      description: "3d / VR / user guides / games"
    },
    {
      quote: "Som en del af vores koncept Neurodivergente Stemmer tager <PERSON> og <PERSON> blandt andet ud på gymnasier og skoler, hvor de holder oplæg og spreder viden om ADHD og autisme. M<PERSON>let er ikke kun at oplyse – men også at skabe rollemodeller, som børn og unge med ADHD og autisme kan spejle sig i.",
      title: "neurodivergente rollemodeller"
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);
    return () => clearInterval(timer);
  }, [slides.length]);

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="fixed top-0 left-0 right-0 bg-black text-white z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <Link href="/" className="text-xl font-light tracking-wide">
              actura element
            </Link>
            
            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-8">
              <Link href="/" className="hover:text-gray-300 transition-colors">Forside</Link>
              <div className="relative group">
                <button className="hover:text-gray-300 transition-colors flex items-center gap-1">
                  ydelser
                  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                  </svg>
                </button>
              </div>
              <Link href="/om-actura" className="hover:text-gray-300 transition-colors">om actura</Link>
              <div className="relative group">
                <button className="hover:text-gray-300 transition-colors flex items-center gap-1">
                  forløb
                  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                  </svg>
                </button>
              </div>
              <div className="relative group">
                <button className="hover:text-gray-300 transition-colors flex items-center gap-1">
                  Aktiviteter
                  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                  </svg>
                </button>
              </div>
              <Link href="/holdet-bag" className="hover:text-gray-300 transition-colors">holdet bag</Link>
              <Link href="/presse" className="hover:text-gray-300 transition-colors">presse</Link>
              <Link href="/kontakt" className="hover:text-gray-300 transition-colors">kontakt</Link>
            </nav>

            {/* Social Icons */}
            <div className="hidden lg:flex items-center space-x-4">
              <a href="https://www.instagram.com/" target="_blank" rel="noopener noreferrer" className="hover:text-gray-300">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zM5.838 12a6.162 6.162 0 1112.324 0 6.162 6.162 0 01-12.324 0zM12 16a4 4 0 110-8 4 4 0 010 8zm4.965-10.405a1.44 1.44 0 112.881.001 1.44 1.44 0 01-2.881-.001z"/>
                </svg>
              </a>
              <a href="https://www.facebook.com/" target="_blank" rel="noopener noreferrer" className="hover:text-gray-300">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
              <a href="https://twitter.com/" target="_blank" rel="noopener noreferrer" className="hover:text-gray-300">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                </svg>
              </a>
            </div>

            {/* Mobile menu button */}
            <button
              className="lg:hidden"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>
      </header>
      {/* Hero Slider Section */}
      <section className="relative bg-black text-white overflow-hidden" style={{ height: '100vh' }}>
        {/* Background Images */}
        <div className="absolute inset-0">
          {/* Slide 1 - Two people */}
          <div className={`absolute inset-0 transition-opacity duration-1000 ${currentSlide === 0 ? 'opacity-100' : 'opacity-0'}`}>
            <img 
              src="https://actura.dk/wp-content/uploads/2025/06/Alvin-Morten-2400-1350-scaled-1280x720.webp" 
              alt="Morten og Alvin" 
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-r from-black/80 via-black/60 to-transparent" />
          </div>
          
          {/* Slide 2 - Tech/3D visual */}
          <div className={`absolute inset-0 transition-opacity duration-1000 ${currentSlide === 1 ? 'opacity-100' : 'opacity-0'}`}>
            <img 
              src="https://actura.dk/wp-content/uploads/2025/06/c-P5240080-scaled-1200x900.jpg" 
              alt="Actura workspace" 
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-r from-black/80 via-black/60 to-transparent" />
          </div>
          
          {/* Slide 3 - Young people learning */}
          <div className={`absolute inset-0 transition-opacity duration-1000 ${currentSlide === 2 ? 'opacity-100' : 'opacity-0'}`}>
            <img 
              src="https://actura.dk/wp-content/uploads/slider/cache/afd84d2becfc00086e3c75ce889e12f6/Philip-presse-til-slider2.jpg" 
              alt="Philip arbejder" 
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-r from-black/80 via-black/60 to-transparent" />
          </div>
        </div>
        
        <div className="relative h-full flex items-center">
          <div className="container mx-auto px-4">
            <div className="grid lg:grid-cols-2 gap-8 items-center">
              {/* Slider Content */}
              <div className="relative">
                {slides.map((slide, index) => (
                  <div
                    key={index}
                    className={`transition-all duration-500 ${index === currentSlide ? 'opacity-100' : 'opacity-0 absolute inset-0'}`}
                  >
                    {slide.quote ? (
                      <div>
                        <blockquote className="text-lg md:text-xl lg:text-2xl font-light leading-relaxed mb-8">
                          {slide.quote}
                        </blockquote>
                        {slide.author && (
                          <p className="text-sm md:text-base text-gray-300 whitespace-pre-line">
                            {slide.author}
                          </p>
                        )}
                      </div>
                    ) : (
                      <div>
                        {slide.title && (
                          <h2 className="text-3xl md:text-4xl lg:text-5xl font-light mb-4">
                            {slide.title}
                          </h2>
                        )}
                        {slide.subtitle && (
                          <h3 className="text-2xl md:text-3xl lg:text-4xl font-light mb-4 text-gray-300">
                            {slide.subtitle}
                          </h3>
                        )}
                        {slide.description && (
                          <p className="text-lg md:text-xl text-gray-300">
                            {slide.description}
                          </p>
                        )}
                      </div>
                    )}
                    {slide.title && !slide.subtitle && (
                      <h2 className="text-3xl md:text-4xl lg:text-5xl font-light mt-8">
                        {slide.title}
                      </h2>
                    )}
                  </div>
                ))}
              </div>
              
              {/* Remove the right column - full width text */}
            </div>
            
            {/* Slider dots */}
            <div className="absolute bottom-8 left-0 right-0 flex justify-center space-x-2">
              {slides.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentSlide(index)}
                  className={`w-2 h-2 rounded-full transition-all ${
                    index === currentSlide ? 'bg-white w-8' : 'bg-white/50'
                  }`}
                  aria-label={`Go to slide ${index + 1}`}
                />
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Main intro section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-5xl mx-auto text-center">
            <h2 className="text-2xl md:text-3xl font-bold mb-4">actura element:</h2>
            <h3 className="text-xl md:text-2xl font-light mb-12">
              En drømme- og talentfabrik<br />
              …. midt i en techvirksomhed
            </h3>
            <div className="text-left space-y-6 text-gray-700">
              <p>
                <strong>Actura Element inviterer indenfor på Islands Brygge</strong>
              </p>
              <p>
                I de gamle pakhuse på Njalsgade giver vi børn og unge med autisme, og med et særligt talent og interessefelt inden for Tech og IT, mulighed for at blive en del af dagligdagen og fællesskabet hos Actura Element.
              </p>
              <p>
                I samspil med fagpersoner i uddannelsessektoren og erhvervs samarbejdspartnere skaber vi rammerne for øget mental trivsel, fastholdelse i skolesystemet og tilknytning til arbejdsmarkedet for den enkelte.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Content Section with Slider */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold">Actura Elements tilbud til Børn og Unge</h2>
          </div>
          
          <div className="max-w-5xl mx-auto space-y-6 text-gray-700 mb-16">
            <p>
              Actura Element er et innovativt talentudviklingsmiljø, hvor børn og unge med autisme/ADHD gives rammerne til at udfolde deres potentiale og dygtiggøre sig indenfor deres særinteresser for IT, Life Science og fintech – kompetencer som virksomheder indenfor Life Science industrien i stigende grad efterspørger.
            </p>
            <p>
              Actura Element bidrager til at skabe <em>kloge hoveder og ekstra hænder</em> til erhvervslivet og øge de unges mentale trivsel i et trygt og levende arbejdsmiljø. Actura Element er udformet som et laboratorium midt i erhvervslivet, hvor de unge finder deres rette element og vejen til deres fremtidige karriere.
            </p>
            <p>
              Børn og unge med autisme/ADHD bliver ofte mødt med uhensigtsmæssige krav, der kan føre til belastningsreaktioner som angst, depression og skolefravær. Et stort antal børn og unge har derfor svært ved at gennemføre en skole- og ungdomsuddannelse og få et arbejde på ordinære vilkår.
            </p>
            <p>
              Vi arbejder for at skabe bedre læringsrammer for børn og unge med autisme/ADHD, og derigennem give dem chancen for at være en del af og bidrage til fællesskabet på lige fod med andre. I partnerskab med andre stærke aktører skaber vi et talentudviklingsmiljø, hvor de unges særlige kompetencer kommer i spil og omdannes til ressourcer i form af innovation og specialviden.
            </p>
          </div>
        </div>
      </section>

      {/* Video Section 1 - Vimeo */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-5xl mx-auto grid lg:grid-cols-2 gap-12 items-center">
            <div className="aspect-video bg-black rounded-lg overflow-hidden">
              <iframe 
                src="https://player.vimeo.com/video/200946937?h=6e9f6d1e8d&title=0&byline=0&portrait=0" 
                className="w-full h-full"
                frameBorder="0" 
                allow="autoplay; fullscreen; picture-in-picture" 
                allowFullScreen
              ></iframe>
            </div>
            <div>
              <blockquote className="text-lg md:text-xl italic text-gray-700 mb-6">
                "Det er fantastisk at se, når lyset vender tilbage i øjnene hos mennesker, der genvinder deres tabte trivsel. Vi oplever, at børn og unge udvikler deres selvtillid og faglige kompetencer, når vi giver dem mulighed for at dyrke deres særlige interesser og talent i et trygt og forudsigeligt arbejdsmiljø blandt voksne ligesindede."
              </blockquote>
              <p className="font-semibold text-gray-700">
                <strong>Morten Olesen,</strong> Direktør Actura
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Video Section 2 - YouTube */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-5xl mx-auto grid lg:grid-cols-2 gap-12 items-center">
            <div className="order-2 lg:order-1">
              <blockquote className="text-lg md:text-xl italic text-gray-700 mb-6">
                "Hi Lady Gaga – I want to tell you how much you mean to me. You help me every day and its actually the second time I see you"
              </blockquote>
              <p className="font-semibold text-gray-700">
                <strong>Ana Maria/Nao</strong>
              </p>
            </div>
            <div className="order-1 lg:order-2 aspect-video bg-black rounded-lg overflow-hidden">
              <iframe 
                className="w-full h-full"
                src="https://www.youtube.com/embed/qdl-vaCRBFY" 
                title="Ana Maria og robotten Nao, Smidstrupfilm.dk" 
                frameBorder="0" 
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                allowFullScreen
              ></iframe>
            </div>
          </div>
        </div>
      </section>

      {/* Image Gallery */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div className="aspect-square bg-gray-300 rounded-lg overflow-hidden relative group">
              <img 
                src="https://actura.dk/wp-content/uploads/2024/07/6c8fcf408862f8d83c9314097cc36e79-aarhus-giver.jpeg" 
                alt="Aarhus giver" 
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300" />
            </div>
            <div className="aspect-square bg-gray-300 rounded-lg overflow-hidden relative group">
              <img 
                src="https://actura.dk/wp-content/uploads/2024/07/1520217544025.jpeg" 
                alt="Actura team" 
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300" />
            </div>
            <div className="aspect-square bg-gray-300 rounded-lg overflow-hidden relative group">
              <img 
                src="https://actura.dk/wp-content/uploads/2024/07/Mathias-Manu-Morten-scaled-1-1024x669.jpeg" 
                alt="Mathias, Manu og Morten" 
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300" />
            </div>
            <div className="aspect-square bg-gray-300 rounded-lg overflow-hidden relative group">
              <img 
                src="https://actura.dk/wp-content/uploads/2024/07/n3fnh3lplrbgilmlqqdsagegzkm-1024x684-1.jpeg" 
                alt="Actura event" 
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300" />
            </div>
            <div className="aspect-square bg-gray-300 rounded-lg overflow-hidden relative group">
              <img 
                src="https://actura.dk/wp-content/uploads/2024/07/overraekkelse-af-model1-1024x575_edited1-2.jpeg" 
                alt="Overrækkelse af model" 
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300" />
            </div>
            <div className="aspect-square bg-gray-300 rounded-lg overflow-hidden relative group">
              <img 
                src="https://actura.dk/wp-content/uploads/2024/07/MG_9373-1024x683.jpg" 
                alt="Actura moment" 
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300" />
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black text-white py-8">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <Link href="/" className="text-xl font-light tracking-wide mb-4 md:mb-0">
              actura element
            </Link>
            
            <div className="flex items-center space-x-6">
              <a href="https://www.instagram.com/" target="_blank" rel="noopener noreferrer" className="hover:text-gray-300">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zM5.838 12a6.162 6.162 0 1112.324 0 6.162 6.162 0 01-12.324 0zM12 16a4 4 0 110-8 4 4 0 010 8zm4.965-10.405a1.44 1.44 0 112.881.001 1.44 1.44 0 01-2.881-.001z"/>
                </svg>
              </a>
              <a href="https://www.facebook.com/profile.php?id=100054413340197" target="_blank" rel="noopener noreferrer" className="hover:text-gray-300">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
