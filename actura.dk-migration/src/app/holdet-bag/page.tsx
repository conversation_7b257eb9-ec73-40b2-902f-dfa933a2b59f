export default function TeamPage() {
  const teamMembers = [
    { name: '<PERSON>', role: 'programmø<PERSON> / App / VR / mentor' },
    { name: '<PERSON>', role: '3D / animation / VR / mentor' },
    { name: '<PERSON><PERSON>', role: '<PERSON><PERSON><PERSON><PERSON><PERSON> / opfinder / mentor' },
    { name: '<PERSON>', role: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> / ai / facetracking' },
    { name: '<PERSON>', role: 'mentor' },
    { name: '<PERSON>', role: 'DevOps / mentor' },
    { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', role: 'Opfinder / 3d print / IT' },
    { name: '<PERSON>', role: '3D / Visuals / mentor' }
  ]

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-8">Holdet bag</h1>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {teamMembers.map((member, index) => (
            <div key={index} className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">{member.name}</h3>
              <p className="text-gray-600">{member.role}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}