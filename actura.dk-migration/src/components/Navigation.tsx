'use client'

import Link from 'next/link'
import { useState } from 'react'

export default function Navigation() {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <nav className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex-shrink-0">
            <Link href="/" className="text-2xl font-bold text-gray-900">
              actura element
            </Link>
          </div>
          
          {/* Desktop Navigation */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-4">
              <Link href="/" className="text-gray-900 hover:text-gray-600 px-3 py-2 rounded-md text-sm font-medium">
                Forside
              </Link>
              
              {/* Services Dropdown */}
              <div className="relative group">
                <button className="text-gray-900 hover:text-gray-600 px-3 py-2 rounded-md text-sm font-medium">
                  Ydelser
                </button>
                <div className="absolute left-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                  <div className="py-1">
                    <Link href="/ai" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Ai Erhvervsløsninger
                    </Link>
                    <Link href="/3d" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      3D, Animation & Visualisering
                    </Link>
                    <Link href="/apps" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Actura Apps
                    </Link>
                  </div>
                </div>
              </div>

              <Link href="/om-actura" className="text-gray-900 hover:text-gray-600 px-3 py-2 rounded-md text-sm font-medium">
                Om actura
              </Link>
              
              {/* Programs Dropdown */}
              <div className="relative group">
                <button className="text-gray-900 hover:text-gray-600 px-3 py-2 rounded-md text-sm font-medium">
                  Forløb
                </button>
                <div className="absolute left-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                  <div className="py-1">
                    <Link href="/teknologitalenter" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Teknologitalenter
                    </Link>
                    <Link href="/stu-accelerate" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      STU og Lab-forløb
                    </Link>
                  </div>
                </div>
              </div>
              
              {/* Activities Dropdown */}
              <div className="relative group">
                <button className="text-gray-900 hover:text-gray-600 px-3 py-2 rounded-md text-sm font-medium">
                  Aktiviteter
                </button>
                <div className="absolute left-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                  <div className="py-1">
                    <Link href="/kalender" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Infomøder og kurser
                    </Link>
                    <Link href="/foredrag" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Foredrag
                    </Link>
                  </div>
                </div>
              </div>

              <Link href="/holdet-bag" className="text-gray-900 hover:text-gray-600 px-3 py-2 rounded-md text-sm font-medium">
                Holdet bag
              </Link>
              <Link href="/presse" className="text-gray-900 hover:text-gray-600 px-3 py-2 rounded-md text-sm font-medium">
                Presse
              </Link>
              <Link href="/kontakt" className="text-gray-900 hover:text-gray-600 px-3 py-2 rounded-md text-sm font-medium">
                Kontakt
              </Link>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="text-gray-900 hover:text-gray-600 focus:outline-none focus:text-gray-600"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {isOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
          
          {/* Social Media Links */}
          <div className="hidden md:flex items-center space-x-4">
            <a href="https://www.instagram.com/" className="text-gray-600 hover:text-gray-900">
              <span className="sr-only">Instagram</span>
              <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.624 5.367 11.99 11.988 11.99s11.988-5.366 11.988-11.99C24.005 5.367 18.641.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.328-1.297L6.53 14.28c.57.621 1.378 1.016 2.284 1.016 1.694 0 3.065-1.371 3.065-3.065s-1.371-3.065-3.065-3.065S5.75 10.537 5.75 12.231c0 .906.395 1.714 1.016 2.284l-1.411 1.409c-.807-.88-1.297-2.031-1.297-3.328 0-2.697 2.188-4.885 4.885-4.885s4.885 2.188 4.885 4.885-2.188 4.885-4.885 4.885z"/>
              </svg>
            </a>
            <a href="https://www.facebook.com/profile.php?id=100054413340197" className="text-gray-600 hover:text-gray-900">
              <span className="sr-only">Facebook</span>
              <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
              </svg>
            </a>
            <a href="https://twitter.com/" className="text-gray-600 hover:text-gray-900">
              <span className="sr-only">Twitter</span>
              <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
              </svg>
            </a>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        {isOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
              <Link href="/" className="text-gray-900 hover:text-gray-600 block px-3 py-2 rounded-md text-base font-medium">
                Forside
              </Link>
              <Link href="/ai" className="text-gray-700 hover:text-gray-600 block px-3 py-2 rounded-md text-base font-medium">
                Ai Erhvervsløsninger
              </Link>
              <Link href="/3d" className="text-gray-700 hover:text-gray-600 block px-3 py-2 rounded-md text-base font-medium">
                3D, Animation & Visualisering
              </Link>
              <Link href="/apps" className="text-gray-700 hover:text-gray-600 block px-3 py-2 rounded-md text-base font-medium">
                Actura Apps
              </Link>
              <Link href="/om-actura" className="text-gray-700 hover:text-gray-600 block px-3 py-2 rounded-md text-base font-medium">
                Om actura
              </Link>
              <Link href="/teknologitalenter" className="text-gray-700 hover:text-gray-600 block px-3 py-2 rounded-md text-base font-medium">
                Teknologitalenter
              </Link>
              <Link href="/stu-accelerate" className="text-gray-700 hover:text-gray-600 block px-3 py-2 rounded-md text-base font-medium">
                STU og Lab-forløb
              </Link>
              <Link href="/kalender" className="text-gray-700 hover:text-gray-600 block px-3 py-2 rounded-md text-base font-medium">
                Infomøder og kurser
              </Link>
              <Link href="/foredrag" className="text-gray-700 hover:text-gray-600 block px-3 py-2 rounded-md text-base font-medium">
                Foredrag
              </Link>
              <Link href="/holdet-bag" className="text-gray-700 hover:text-gray-600 block px-3 py-2 rounded-md text-base font-medium">
                Holdet bag
              </Link>
              <Link href="/presse" className="text-gray-700 hover:text-gray-600 block px-3 py-2 rounded-md text-base font-medium">
                Presse
              </Link>
              <Link href="/kontakt" className="text-gray-700 hover:text-gray-600 block px-3 py-2 rounded-md text-base font-medium">
                Kontakt
              </Link>
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}